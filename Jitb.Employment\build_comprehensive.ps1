param(
    [string]$Configuration = "Debug",
    [string]$Platform = "Any CPU"
)

Write-Host "=== Jitb.Employment Solution Build Script ===" -ForegroundColor Green
Write-Host "Configuration: $Configuration" -ForegroundColor Yellow
Write-Host "Platform: $Platform" -ForegroundColor Yellow
Write-Host ""

# Get the script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$SolutionFile = Join-Path $ScriptDir "Jitb.Employment.sln"

# Check if solution file exists
if (-not (Test-Path $SolutionFile)) {
    Write-Error "Solution file not found: $SolutionFile"
    exit 1
}

Write-Host "Solution file: $SolutionFile" -ForegroundColor Cyan

# Function to try dotnet CLI approach
function Try-DotNetBuild {
    Write-Host "Attempting build with .NET CLI..." -ForegroundColor Yellow
    
    try {
        $dotnetVersion = dotnet --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Found .NET CLI version: $dotnetVersion" -ForegroundColor Green
            
            Write-Host "Restoring packages with dotnet..." -ForegroundColor Cyan
            dotnet restore $SolutionFile --verbosity minimal
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "Building with dotnet..." -ForegroundColor Cyan
                dotnet build $SolutionFile --configuration $Configuration --verbosity minimal --no-restore
                
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "✓ Build successful with .NET CLI!" -ForegroundColor Green
                    return $true
                }
                else {
                    Write-Host "✗ Build failed with .NET CLI (Exit code: $LASTEXITCODE)" -ForegroundColor Red
                }
            }
            else {
                Write-Host "✗ Package restore failed with .NET CLI" -ForegroundColor Red
            }
        }
    }
    catch {
        Write-Host "✗ .NET CLI not available or failed: $_" -ForegroundColor Red
    }
    
    return $false
}

# Function to try MSBuild approach
function Try-MSBuild {
    Write-Host "Attempting build with MSBuild..." -ForegroundColor Yellow
    
    # Try to find MSBuild
    $MSBuildPaths = @(
        "${env:ProgramFiles}\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe",
        "${env:ProgramFiles}\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe",
        "${env:ProgramFiles}\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe",
        "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Enterprise\MSBuild\Current\Bin\MSBuild.exe",
        "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe",
        "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe",
        "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2017\Enterprise\MSBuild\15.0\Bin\MSBuild.exe",
        "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2017\Professional\MSBuild\15.0\Bin\MSBuild.exe",
        "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2017\Community\MSBuild\15.0\Bin\MSBuild.exe",
        "${env:ProgramFiles(x86)}\MSBuild\14.0\Bin\MSBuild.exe"
    )

    $MSBuildPath = $null
    foreach ($path in $MSBuildPaths) {
        if (Test-Path $path) {
            $MSBuildPath = $path
            break
        }
    }

    if (-not $MSBuildPath) {
        Write-Host "✗ MSBuild not found" -ForegroundColor Red
        return $false
    }

    Write-Host "Found MSBuild: $MSBuildPath" -ForegroundColor Green

    # Try to find NuGet
    $NuGetPath = $null
    try {
        $result = Get-Command "nuget.exe" -ErrorAction SilentlyContinue
        if ($result) {
            $NuGetPath = $result.Source
        }
    }
    catch {
        # NuGet not in PATH
    }

    if (-not $NuGetPath) {
        Write-Host "NuGet not found in PATH. Trying to download..." -ForegroundColor Yellow
        $NuGetPath = Join-Path $ScriptDir "nuget.exe"
        if (-not (Test-Path $NuGetPath)) {
            try {
                Write-Host "Downloading NuGet..." -ForegroundColor Cyan
                Invoke-WebRequest -Uri "https://dist.nuget.org/win-x86-commandline/latest/nuget.exe" -OutFile $NuGetPath
            }
            catch {
                Write-Host "✗ Failed to download NuGet: $_" -ForegroundColor Red
                return $false
            }
        }
    }

    Write-Host "Found NuGet: $NuGetPath" -ForegroundColor Green

    try {
        Write-Host "Restoring packages with NuGet..." -ForegroundColor Cyan
        & $NuGetPath restore $SolutionFile -NonInteractive
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "⚠ NuGet restore completed with warnings (Exit code: $LASTEXITCODE)" -ForegroundColor Yellow
        }

        Write-Host "Building with MSBuild..." -ForegroundColor Cyan
        & $MSBuildPath $SolutionFile /p:Configuration=$Configuration /p:Platform="$Platform" /m /v:m /p:WarningLevel=1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Build successful with MSBuild!" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "✗ Build failed with MSBuild (Exit code: $LASTEXITCODE)" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "✗ MSBuild failed: $_" -ForegroundColor Red
    }
    
    return $false
}

# Function to show project structure
function Show-ProjectStructure {
    Write-Host "=== Project Structure ===" -ForegroundColor Green
    
    $srcPath = Join-Path $ScriptDir "src"
    if (Test-Path $srcPath) {
        Write-Host "Source projects:" -ForegroundColor Cyan
        Get-ChildItem $srcPath -Directory | ForEach-Object {
            $csprojFiles = Get-ChildItem $_.FullName -Filter "*.csproj" -ErrorAction SilentlyContinue
            if ($csprojFiles) {
                Write-Host "  - $($_.Name)" -ForegroundColor White
            }
        }
    }
    
    Write-Host ""
}

# Function to check prerequisites
function Check-Prerequisites {
    Write-Host "=== Checking Prerequisites ===" -ForegroundColor Green
    
    # Check .NET Framework
    $dotNetVersions = Get-ChildItem "HKLM:SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full\" -ErrorAction SilentlyContinue | Get-ItemProperty -Name Release -ErrorAction SilentlyContinue | Where-Object { $_.Release -ge 461808 }
    if ($dotNetVersions) {
        Write-Host "✓ .NET Framework 4.6.1+ is installed" -ForegroundColor Green
    }
    else {
        Write-Host "⚠ .NET Framework 4.6.1+ may not be installed" -ForegroundColor Yellow
    }
    
    # Check if packages directory exists
    $packagesPath = Join-Path $ScriptDir "packages"
    if (Test-Path $packagesPath) {
        $packageCount = (Get-ChildItem $packagesPath -Directory).Count
        Write-Host "✓ Packages directory exists with $packageCount packages" -ForegroundColor Green
    }
    else {
        Write-Host "⚠ Packages directory not found - packages need to be restored" -ForegroundColor Yellow
    }
    
    Write-Host ""
}

# Main execution
Show-ProjectStructure
Check-Prerequisites

# Try different build approaches
$buildSuccess = $false

# First try .NET CLI
if (Try-DotNetBuild) {
    $buildSuccess = $true
}
# If that fails, try MSBuild
elseif (Try-MSBuild) {
    $buildSuccess = $true
}

if ($buildSuccess) {
    Write-Host ""
    Write-Host "🎉 BUILD COMPLETED SUCCESSFULLY! 🎉" -ForegroundColor Green -BackgroundColor Black
    Write-Host ""
    
    # Show output directories
    Write-Host "=== Build Output ===" -ForegroundColor Green
    $srcPath = Join-Path $ScriptDir "src"
    if (Test-Path $srcPath) {
        Get-ChildItem $srcPath -Directory | ForEach-Object {
            $binPath = Join-Path $_.FullName "bin\$Configuration"
            if (Test-Path $binPath) {
                $files = Get-ChildItem $binPath -File | Where-Object { $_.Extension -in @('.dll', '.exe') }
                if ($files) {
                    Write-Host "  $($_.Name):" -ForegroundColor Cyan
                    $files | ForEach-Object {
                        Write-Host "    - $($_.Name)" -ForegroundColor White
                    }
                }
            }
        }
    }
}
else {
    Write-Host ""
    Write-Host "❌ BUILD FAILED!" -ForegroundColor Red -BackgroundColor Black
    Write-Host ""
    Write-Host "Troubleshooting suggestions:" -ForegroundColor Yellow
    Write-Host "1. Ensure Visual Studio or Build Tools are installed" -ForegroundColor White
    Write-Host "2. Check that all NuGet package sources are accessible" -ForegroundColor White
    Write-Host "3. Verify .NET Framework 4.6.1 is installed" -ForegroundColor White
    Write-Host "4. Check for any missing project references" -ForegroundColor White
    exit 1
}