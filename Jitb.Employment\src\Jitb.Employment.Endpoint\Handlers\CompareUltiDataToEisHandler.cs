using System.Threading.Tasks;

using System.Threading.Tasks;

namespace Jitb.Employment.Endpoint.Handlers
{
    using Contracts.Commands.Employment;
    using Jitb.Employment.Domain.Concepts;
    using Jitb.Employment.Domain.Providers;
    using Jitb.Employment.Domain.Repositories.Employment;
    using NServiceBus;
    using NServiceBus.Logging;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using static Jitb.Employment.Domain.Providers.UltiproToEisDataComparer;

    public class CompareUltiDataToEisHandler : IHandleMessages<CompareUltiDataToEisCommand>
    {
        private static ILog Log = LogManager.GetLogger(typeof(CompareUltiDataToEisHandler));
        private readonly IEmployeeRepository _employeeRepository;
        private IUltiproToEisDataComparer _ultiproToEisDataComparer;
        private readonly IUltiProToEisDataMismatchRepository _ultiProToEisDataMismatchRepository;

        public CompareUltiDataToEisHandler(IEmployeeRepository employeeRepository,
            IUltiProToEisDataMismatchRepository ultiProToEisDataMismatchRepository)
        {
            this._employeeRepository = employeeRepository;
            this._ultiProToEisDataMismatchRepository = ultiProToEisDataMismatchRepository;
        }

        public async Task Handle(CompareUltiDataToEisCommand message, IMessageHandlerContext context)
        {
            Log.Info(
                $"Handling CompareUltiDataToEisCommand for Employee ID: {message.EmployeeId} (Employee HR Id: {message.UltiProEmployeeId})");

            var eisEmployee = _employeeRepository.GetEmployeeByUltiProId(message.UltiProEmployeeId);

            // If unable to find the employee in EIS send a notification, else compare the data we brought back from EIS
            if (eisEmployee == null)
            {
                Log.Debug(
                    $"Employee does not exist in EIS - UltiPro Employee Id: {message.UltiProEmployeeId} Name:{message.FirstName} {message.LastName}");

                this._ultiProToEisDataMismatchRepository.Add(new List<UltiProToEisDataMismatch>()
                {
                    new UltiProToEisDataMismatch()
                    {
                        ParentRunId = message.RunId, Severity = UltiProToEisDataMismatchSeverity.Critical,
                        TestType = ComparisonTestType.EmployeeNotFoundInEIS, CoreEmployeeId = message.EmployeeId,
                        HrEmployeeId = message.HrEmployeeNumber, HrEmployeeIdentityId = message.UltiProEmployeeId,
                        Message =
                            $"* Employee does not exist in EIS - UltiPro Employee Id: {message.UltiProEmployeeId} Name:{message.FirstName} {message.LastName}"
                    }
                });
            }
            else
            {
                Log.Debug(
                    $"Found Employee in EIS - UltiProId/HREmployeeId: {message.UltiProEmployeeId}, Employee Id: {eisEmployee.EmployeeId}, Name:{message.FirstName} {message.LastName}");

                // We now have the Ultipro record (message) and the matching EIS Employee Record, now compare all the data points
                _ultiproToEisDataComparer = new UltiproToEisDataComparer();

                List<UltiProToEisDataMismatch> comparisonFailuresList =
                    _ultiproToEisDataComparer.RunDataComparison(message, eisEmployee);
                string comparisonFailures = string.Join(System.Environment.NewLine, comparisonFailuresList);

                if (comparisonFailuresList.Count > 0)
                {
                    Log.Debug(
                        $"Comparison failure - {string.Join(System.Environment.NewLine, comparisonFailuresList)}");

                    // Log to repository          
                    Log.Debug($"Logging failure to repository: {comparisonFailures}");

                    this._ultiProToEisDataMismatchRepository.Add(comparisonFailuresList);
                    await this.CheckIfUltiProEISEmailsNeedToSync(message, context, comparisonFailuresList, eisEmployee);
                }

                eisEmployee.LastReceivedFromUltiPro = DateTime.Now;
                this._employeeRepository.Add(eisEmployee);
            }

            await Task.CompletedTask;
        }

        private async Task CheckIfUltiProEISEmailsNeedToSync(CompareUltiDataToEisCommand message,
            IMessageHandlerContext context,
            List<UltiProToEisDataMismatch> comparisonFailuresList, Employee eisEmployee)
        {
            // if any email is not in sync, send it on so we can do a proper comparison
            var needToCompareEmails = comparisonFailuresList.Where(c =>
                (c.TestType == ComparisonTestType.AlternateEmail ||
                 c.TestType == ComparisonTestType.PrimaryEmail) == true).ToList();

            // only sync if we have 'active' employees...
            if (needToCompareEmails.Count > 0 && eisEmployee.IsTerminated == false)
            {
                Log.Debug(
                    $"Sending need to compare emails for UltiProId/HREmployeeId: {message.UltiProEmployeeId}, Employee Id: {eisEmployee.EmployeeId}, Name:{message.FirstName} {message.LastName} (emails) Primary:{message.EmailAddress} Alt: {message.AlternateEmailAddress}");
                // we'll send this on to see if we need to sync Ultipro and EIS -- then determine if we need to sync eRestaurant.
                //    await context.Send(new NeedToCompareUltiproEISEmail()
                //    {
                //        FirstName                         = message.FirstName,
                //        LastName                          = message.LastName,
                //        AlternateEmailAddress             = message.AlternateEmailAddress,
                //        EmailAddress                      = message.EmailAddress,
                //        EmployeeId                        = eisEmployee.EmployeeId,
                //        HrEmployeeNumber                  = message.HrEmployeeNumber,
                //        RunId                             = message.RunId,
                //        UltiProEmployeeId                 = message.UltiProEmployeeId,
                //        UltiProToEisDataComparisonCheckId = message.UltiProToEisDataComparisonCheckId
                //    });
                //}

                await Task.CompletedTask;
            }
        }
    }
}



