﻿using Afterman.nRepo.NHibernate;


namespace Afterman.nRepo.Configuration
{
    using Afterman.nRepo.InMemory;

    public class RepositorySelector : IRepositorySelector
    {

        public NHibernateConfiguration NHibernate()
        {
            return new NHibernateConfiguration();
        }

        public InMemoryConfiguration InMemory()
        {
            return new InMemoryConfiguration();
        }
    }
}
