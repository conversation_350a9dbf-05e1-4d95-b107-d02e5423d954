using System.Threading.Tasks;
using System.Threading.Tasks;

namespace Jitb.Employment.Endpoint.Handlers
{
    using Contracts.Commands.Employment;
    using Contracts.Events.Employment;
    using Domain.Concepts;
    using Domain.Integrations.Components.Core;
    using Domain.Repositories.Employment;
    using NServiceBus;
    using NServiceBus.Logging;

    public class ChangeJobCodeDefinitionHandler : 
        IHandleMessages<ChangeJobCodeDefinition>
    {
        private readonly ITranslateCommands<ChangeJobCodeDefinition, JobCode> _jobCodeDefinitionTranslator;
        private static readonly ILog Log = LogManager.GetLogger(typeof(ChangeJobCodeDefinitionHandler));
        private readonly IJobCodeRepository _jobCodeRepository;

        public ChangeJobCodeDefinitionHandler(IJobCodeRepository jobCodeRepository
            , ITranslateCommands<ChangeJobCodeDefinition, JobCode> jobCodeDefinitionTranslator)
        {
            this._jobCodeRepository = jobCodeRepository;
            this._jobCodeDefinitionTranslator = jobCodeDefinitionTranslator;
        }

        public async Task Handle(ChangeJobCodeDefinition message, IMessageHandlerContext context)
        {
            Log.Info($"Updating for job code {message.JobCode}");
            var jobCode = this._jobCodeRepository.GetByCode(message.JobCode);
            jobCode = this._jobCodeDefinitionTranslator.MapTo(message, jobCode);
            this._jobCodeRepository.Add(jobCode);
            Log.Info($"job code {message.JobCode} updated");
            await context.Publish<IChangedAJobCodeDefinition>(x =>
            {
                x.JobCode = message.JobCode;
            });
            await Task.CompletedTask;
        }
    }
}
