using System;
using System.Threading.Tasks;
using FluentAssertions;
using Jitb.Employment.HarriCaller.Domain.RateLimiting;
using NLog;
using ServiceStack.Redis;
using Xunit;
using Xunit.Abstractions;

namespace Jitb.Employment.HarriCaller.Domain.Tests
{
    public class RateLimitRedisSimpleTest : IDisposable
    {
        private readonly ITestOutputHelper _output;
        private readonly ILogger _logger;
        private readonly string _redisConnectionString;

        public RateLimitRedisSimpleTest(ITestOutputHelper output)
        {
            _output = output;
            _logger = LogManager.GetCurrentClassLogger();
            _redisConnectionString = "localhost:6379"; // Use local Redis for testing
            _output.WriteLine($"Using Redis connection: {_redisConnectionString}");
        }

        [Fact]
        public async Task RecordRequestAsync_ShouldWriteToRedis_LocalTest()
        {
            if (!await IsRedisAvailable())
            {
                _output.WriteLine("Redis is not available locally. Skipping test.");
                return;
            }

            try
            {
                using (var redisClient = new RedisClient(_redisConnectionString))
                {
                    var key = $"test-redis-verify-{Guid.NewGuid()}";

                    redisClient.Set(key, "test-value");
                    var result = redisClient.Get<string>(key);

                    result.Should().Be("test-value");

                    redisClient.Remove(key);
                }
            }
            catch (Exception ex)
            {
                _output.WriteLine($"Test failed with exception: {ex.Message}");
                throw;
            }
        }

        [Fact]
        public async Task TryMakeRequestAsync_ShouldCreateUniqueEntries()
        {
            if (!await IsRedisAvailable())
            {
                _output.WriteLine("Redis is not available locally. Skipping test.");
                return;
            }

            RateLimitRedisFixed rateLimiter = null;
            try
            {
                rateLimiter = new RateLimitRedisFixed(_redisConnectionString, _logger, 10, "test-unique:");
                var key = $"test-unique-{Guid.NewGuid()}";

                using (var redisClient = new RedisClient(_redisConnectionString))
                {
                    redisClient.AddItemToList(key, "entry1");
                    redisClient.AddItemToList(key, "entry2");
                    redisClient.AddItemToList(key, "entry3");

                    var entries = redisClient.GetAllItemsFromList(key);
                    _output.WriteLine($"Entries in list:");
                    foreach (var entry in entries)
                    {
                        _output.WriteLine($"  Entry: {entry}");
                    }

                    entries.Count.Should().Be(3, "Should have 3 unique entries");

                    redisClient.Remove(key);
                }
            }
            catch (Exception ex)
            {
                _output.WriteLine($"Test failed with exception: {ex.Message}");
                throw;
            }
            finally
            {
                rateLimiter?.Dispose();
            }
        }

        private async Task<bool> IsRedisAvailable()
        {
            try
            {
                using (var redisClient = new RedisClient(_redisConnectionString))
                {
                    redisClient.Ping();
                    return true;
                }
            }
            catch (Exception ex)
            {
                _output.WriteLine($"Redis not available: {ex.Message}");
                return false;
            }
        }

        public void Dispose()
        {
            // Cleanup if needed
        }
    }
}
