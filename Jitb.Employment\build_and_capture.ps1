Write-Host "Building solution and capturing output..." -ForegroundColor Green
Write-Host ""

# Clear any previous build artifacts
Write-Host "Cleaning solution..." -ForegroundColor Cyan
dotnet clean Jitb.Employment.sln

# Restore packages
Write-Host "Restoring packages..." -ForegroundColor Cyan
dotnet restore Jitb.Employment.sln --verbosity normal

# Build and capture output
Write-Host "Building solution..." -ForegroundColor Cyan
$buildOutput = dotnet build Jitb.Employment.sln --configuration Debug --verbosity normal 2>&1

# Save output to file
$buildOutput | Out-File -FilePath "detailed_build_output.txt" -Encoding UTF8

# Display output
$buildOutput

if ($LASTEXITCODE -eq 0) {
    Write-Host ""
    Write-Host "✓ Build completed successfully!" -ForegroundColor Green
} else {
    Write-Host ""
    Write-Host "✗ Build failed. Check detailed_build_output.txt for full details" -ForegroundColor Red
    
    # Show just the errors
    Write-Host ""
    Write-Host "=== BUILD ERRORS ===" -ForegroundColor Red
    $buildOutput | Where-Object { $_ -match "error CS" -or $_ -match "Error:" } | ForEach-Object {
        Write-Host $_ -ForegroundColor Red
    }
}