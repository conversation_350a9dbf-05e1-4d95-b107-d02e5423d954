using System.Collections.Generic;
using System.Collections.Generic;
using System.Threading.Tasks;
using Jitb.Employment.Contracts.Commands.Employment;
using Jitb.Employment.Contracts.Commands.Employment.Reply;
using Jitb.Employment.Domain.Repositories.Employment;
using NServiceBus;
using NServiceBus.Logging;

namespace Jitb.Employment.Endpoint.Handlers
{
    public class AnnounceLocationHandler : IHandleMessages<AnnounceLocation>
    {
        static readonly ILog Log = LogManager.GetLogger(typeof(AnnounceLocation));
        private IEmployeeRepository _employeeRepository;
        private ILocationRepository _locationRepository;

        public AnnounceLocationHandler(IEmployeeRepository employeeRepository
            , ILocationRepository locationRepository)
        {
            _employeeRepository = employeeRepository;
            _locationRepository = locationRepository;
        }

        public async Task Handle(AnnounceLocation message, IMessageHandlerContext context)
        {
            Log.Info($"Announcing entire location {message.LocationNumber}");
            var location = _locationRepository.GetByLocationNumber(message.LocationNumber);
            var reply = new AnnounceLocationReply()
            {
                EmployeesAnnounced = new List<long>(),
                LocationNumber = message.LocationNumber,
            };
            var employees = _employeeRepository.GetEmployeesToConvertToErestaurantByLocation(location);
            foreach (var employee in employees)
            {
                Log.Info($"sending employee {employee.EmployeeId}");
                reply.EmployeesAnnounced.Add(employee.EmployeeId);
                await context.Send<AnnounceEmployee>(x =>
                {
                    x.EmployeeId = employee.EmployeeId;
                });
            }
            if (message.ExpectReply)
            {
                await context.Reply(reply);
            }
        }
    }
}
