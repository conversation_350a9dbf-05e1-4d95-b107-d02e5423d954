Write-Host "Fixing NuGet package issues..." -ForegroundColor Green
Write-Host ""

# Clear NuGet caches
Write-Host "Clearing NuGet caches..." -ForegroundColor Cyan
dotnet nuget locals all --clear

# Try to restore with force
Write-Host "Restoring packages with --force..." -ForegroundColor Cyan
dotnet restore Jitb.Employment.sln --force --verbosity detailed

# If that fails, try with --no-cache
if ($LASTEXITCODE -ne 0) {
    Write-Host "Trying restore with --no-cache..." -ForegroundColor Yellow
    dotnet restore Jitb.Employment.sln --no-cache --verbosity detailed
}

# Check if packages directory exists and has content
$packagesPath = "packages"
if (Test-Path $packagesPath) {
    $packageCount = (Get-ChildItem $packagesPath -Directory).Count
    Write-Host "Packages directory has $packageCount packages" -ForegroundColor Green
} else {
    Write-Host "Packages directory not found" -ForegroundColor Red
}

Write-Host ""
Write-Host "Package restore attempt completed. Try building now with:" -ForegroundColor Yellow
Write-Host "dotnet build Jitb.Employment.sln" -ForegroundColor White