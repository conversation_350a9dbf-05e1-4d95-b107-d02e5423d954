using System.Threading.Tasks;
using NServiceBus;
using NServiceBus.Logging;
using System.Threading.Tasks;

namespace Jitb.Employment.Endpoint.Handlers
{
    using Contracts.Commands.Employment;
    using Contracts.Events.Employment;
    using Facade.Actions.Core;
    using System;

    public class AnnounceEmployeeHandler : 
        IHandleMessages<AnnounceEmployee>
        , IHandleMessages<BulkAnnounceEmployees>
    {
        private static readonly ILog Log = LogManager.GetLogger<AnnounceEmployeeHandler>();
        private readonly IEmployeeAnnoucementProvider _employeeAnnouncementProvider;

        public AnnounceEmployeeHandler(IEmployeeAnnoucementProvider employeeAnnoucementProvider)
        {
            this._employeeAnnouncementProvider = employeeAnnoucementProvider;
        }
        public async Task Handle(AnnounceEmployee message, IMessageHandlerContext context)
        {

            var employee = this._employeeAnnouncementProvider
                .GetEmployeeForAnnouncement(message.EmployeeId);

            Log.Info($"employee {employee.EmployeeId} being announced...");

            await context.Publish<IAnnouncedAnEmployee>(x =>
                {
                    x.EmployeeId = message.EmployeeId;
                });
        }

        public async Task Handle(BulkAnnounceEmployees message, IMessageHandlerContext context)
        {
            foreach (var employeeId in message.EmployeeIds)
            {
                
                await context.Send<AnnounceEmployee>(x =>
                {
                    x.EmployeeId = employeeId;
                });
            }
        }
    }
}
