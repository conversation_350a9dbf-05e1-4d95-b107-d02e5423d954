﻿<Project Sdk="Microsoft.NET.Sdk.WindowsDesktop">

  <PropertyGroup>
    <TargetFramework>net461</TargetFramework>
    <RootNamespace>Jitb.Employment.HarriCaller.Domain</RootNamespace>
    <AssemblyName>Jitb.Employment.HarriCaller.Domain</AssemblyName>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="StackExchange.Redis" Version="2.6.66" />
    <PackageReference Include="NLog" Version="5.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Jitb.Employment.Domain\Jitb.Employment.Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Include="RateLimiting\IRateLimit.cs" />
    <Compile Include="RateLimiting\RateLimitMemory.cs" />
    <Compile Include="RateLimiting\RateLimitRedis.cs" />
    <Compile Include="RateLimiting\RateLimitRedisFixed.cs" />
    <Compile Include="Providers\GetHarriEmployeeProvider.cs" />
    <Compile Include="Providers\CallHarriWebServiceProvider.cs" />
    <Compile Include="DomainRegistry.cs" />
  </ItemGroup>

</Project>