﻿<Project Sdk="Microsoft.NET.Sdk.WindowsDesktop">

  <PropertyGroup>
    <TargetFramework>net461</TargetFramework>
    <RootNamespace>Jitb.Employment.HarriCaller.Domain</RootNamespace>
    <AssemblyName>Jitb.Employment.HarriCaller.Domain</AssemblyName>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="StackExchange.Redis" Version="2.6.66" />
    <PackageReference Include="NLog" Version="5.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="RestSharp" Version="110.2.0" />
    <PackageReference Include="JetBrains.Annotations" Version="2023.3.0" />
    <PackageReference Include="StructureMap" Version="4.7.1" />
    <PackageReference Include="ValueOf" Version="2.0.31" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Jitb.Employment.Domain\Jitb.Employment.Domain.csproj" />
    <ProjectReference Include="..\Afterman.nRepo\Afterman.nRepo.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Include="RateLimiting\IRateLimit.cs" />
    <Compile Include="RateLimiting\RateLimitMemory.cs" />
    <Compile Include="RateLimiting\RateLimitRedis.cs" />
    <Compile Include="RateLimiting\RateLimitRedisFixed.cs" />
    <Compile Include="Providers\GetHarriEmployeeProvider.cs" />
    <Compile Include="Providers\CallHarriWebServiceProvider.cs" />
    <Compile Include="Providers\CallHarriWebserviceProviderExtended.cs" />
    <Compile Include="Providers\HarriUrlGenerator.cs" />
    <Compile Include="Providers\HarriUrlGeneratorFactory.cs" />
    <Compile Include="Providers\IHarriUrlGeneratorOld.cs" />
    <Compile Include="Providers\TenantByLocationProvider.cs" />
    <Compile Include="DomainRegistry.cs" />
    <Compile Include="Concepts\HarriEmployeeAbsence.cs" />
    <Compile Include="Concepts\HarriEmployeeMappings.cs" />
    <Compile Include="Concepts\HarriEmploymentPeriod.cs" />
    <Compile Include="Concepts\HarriEvents.cs" />
    <Compile Include="Concepts\HarriInboundEmployee.cs" />
    <Compile Include="Concepts\HarriInboundEmployeeData.cs" />
    <Compile Include="Concepts\HarriLocation.cs" />
    <Compile Include="Concepts\HarriLocations.cs" />
    <Compile Include="Concepts\HarriMapping.cs" />
    <Compile Include="Concepts\HarriMappingData.cs" />
    <Compile Include="Concepts\HarriMappings.cs" />
    <Compile Include="Concepts\HarriPosition.cs" />
    <Compile Include="Concepts\HarriTerminationReason.cs" />
    <Compile Include="Concepts\UnmappedEmployee.cs" />
    <Compile Include="Constants\HarriCrossLocationModes.cs" />
    <Compile Include="Constants\HarriPayTypes.cs" />
    <Compile Include="Constants\HarriStatusCodes.cs" />
    <Compile Include="Constants\JsonSerializer.cs" />
    <Compile Include="Constants\PayloadMappingEndpoints.cs" />
    <Compile Include="Enums\CallTypes.cs" />
    <Compile Include="Enums\TerminationReasons.cs" />
    <Compile Include="Enums\UrlType.cs" />
    <Compile Include="Exceptions\InvalidCallToHarriException.cs" />
    <Compile Include="Exceptions\TenantNotFoundForLocationException.cs" />
    <Compile Include="HarriPayloads\Address.cs" />
    <Compile Include="HarriPayloads\EmploymentPeriod.cs" />
    <Compile Include="HarriPayloads\HarriAttachLocationPayload.cs" />
    <Compile Include="HarriPayloads\HarriChangeAnnualPayRatePayload.cs" />
    <Compile Include="HarriPayloads\HarriChangeHourlyPayRatePayload.cs" />
    <Compile Include="HarriPayloads\HarriChangePositionPayload.cs" />
    <Compile Include="HarriPayloads\HarriEndSharePayload.cs" />
    <Compile Include="HarriPayloads\HarriMappingPayload.cs" />
    <Compile Include="HarriPayloads\HarriNewHirePayload.cs" />
    <Compile Include="HarriPayloads\HarriNewPayTypePayload.cs" />
    <Compile Include="HarriPayloads\HarriPositionPayload.cs" />
    <Compile Include="HarriPayloads\HarriPostMappingsWrapperPayload.cs" />
    <Compile Include="HarriPayloads\HarriRehirePayload.cs" />
    <Compile Include="HarriPayloads\HarriSchedulePayload.cs" />
    <Compile Include="HarriPayloads\HarriStartLoaPayload.cs" />
    <Compile Include="HarriPayloads\HarriTerminationPayload.cs" />
    <Compile Include="HarriPayloads\HarriUpdateEmployeePayload.cs" />
    <Compile Include="HarriPayloads\Pagination.cs" />
    <Compile Include="HarriPayloads\PayRate.cs" />
    <Compile Include="HarriPayloads\PayType.cs" />
    <Compile Include="Position.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="SerializerSettings\DateFormatConverter.cs" />
    <Compile Include="TokenResponse.cs" />
    <Compile Include="ValueObjects\Address.cs" />
  </ItemGroup>

</Project>