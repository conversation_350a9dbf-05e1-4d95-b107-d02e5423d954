﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace Afterman.nRepo.InMemory
{
    using System.Data;
    using Afterman.nRepo.Configuration;
    public class InMemoryDataAccessor<T> : IDataAccessor<T>
        where T : class
    {
        private static Dictionary<Object,T> _inMemoryStore = new Dictionary<Object,T>();
        public void Dispose()
        {
            
        }

        public void SetIsolationLevel(IsolationLevel level)
        {
            
        }

        public void Add(T entity)
        {
            _inMemoryStore.Add(entity.GetHashCode(), entity);
        }

        public void Remove(T entity)
        {
            if (_inMemoryStore.ContainsKey(entity.GetHashCode()))
                _inMemoryStore.Remove(entity);
        }

        public void Remove(IList<T> entities)
        {
            foreach(var entity in entities)
                Remove(entity);
        }

        public T Get(object key)
        {
            if (_inMemoryStore.ContainsKey(key)) return _inMemoryStore[key];
            return default(T);
        }

        public IList<T> GetAll()
        {
            return _inMemoryStore
                .Values
                .ToList();
        }

        public IList<T> GetAll(int pageSize, int pageNumber)
        {
            return _inMemoryStore
                .Values
                .ToList()
                .AsQueryable()
                .Skip(pageSize*pageNumber)
                .Take(pageSize)
                .ToList();
        }

        public void BeginTransaction()
        {
            
        }

        public void CommitTransaction()
        {
            
        }

        public void RollbackTransaction()
        {
            
        }

        public void Add(IList<T> entities)
        {
            foreach (var entity in entities)
                Add(entity);
        }

        public IQueryable<T> CreateQuery()
        {
            return _inMemoryStore
                .Values
                .ToList()
                .AsQueryable();
        }

        public IList<T> ExecuteQuery(string query)
        {
            throw new NotImplementedException();
        }
    }
}
