# Jitb.Employment Solution Build Guide

This document provides instructions for building the Jitb.Employment solution.

## Prerequisites

- **Visual Studio 2017 or later** (or Visual Studio Build Tools)
- **.NET Framework 4.6.1 or later**
- **NuGet CLI** (will be downloaded automatically if not available)
- **Access to NuGet package sources** (including private Azure DevOps feed)

## Quick Start

### Option 1: Using the Comprehensive Build Script (Recommended)

```powershell
.\build_comprehensive.ps1
```

This script will:
- Check prerequisites
- Show project structure
- Try multiple build approaches (.NET CLI, then MSBuild)
- Download NuGet if needed
- Restore packages and build the solution

### Option 2: Using .NET CLI

```powershell
.\build_dotnet.ps1
```

### Option 3: Using MSBuild

```powershell
.\build_solution.ps1
```

### Option 4: Using Batch File

```cmd
build.bat
```

## Build Configurations

The solution supports multiple build configurations:

- **Debug** (default)
- **Release**
- **QA**
- **Azure**
- **prod**
- **aws.prod**
- **aws.qa**

To build with a specific configuration:

```powershell
.\build_comprehensive.ps1 -Configuration Release
```

## Project Structure

The solution contains the following main projects:

### Core Projects
- **Jitb.Employment.Contracts** - Message contracts and interfaces
- **Jitb.Employment.Domain** - Business logic and domain entities
- **Jitb.Employment.Facade** - API facade layer

### Endpoints (NServiceBus)
- **Jitb.Employment.Endpoint** - Main employment endpoint
- **Jitb.Employment.ActiveDirectory.Endpoint** - Active Directory integration
- **Jitb.Employment.Audit.Endpoint** - Audit logging endpoint
- **Jitb.Employment.AD.Sync.Endpoint** - AD synchronization service

### Web Services
- **JIB.JobApp.WebService** - Job application web service
- **JIB.Lawson.Services.WebService** - Lawson integration web service

### Utilities
- **InjectMessage** - Message injection utility
- **Afterman.nRepo** - Repository pattern implementation

## Package Management

The solution uses both public NuGet packages and private packages from Azure DevOps:

- **Public packages**: nuget.org
- **Private packages**: Azure DevOps feed (JackInTheBox-NServiceBus)

Authentication for the private feed is configured in `NuGet.config`.

## Build Output

After a successful build, compiled assemblies will be available in:
```
src\[ProjectName]\bin\[Configuration]\
```

## Packaging

To package the solution for deployment:

```powershell
cd build
.\package_build.ps1 qa debug
```

This will create deployment packages in the `package` directory.

## Deployment

To deploy to an environment:

```powershell
cd build
.\release_build.ps1 qa
```

## Troubleshooting

### Common Issues

1. **NuGet restore fails**
   - Check internet connectivity
   - Verify Azure DevOps credentials in NuGet.config
   - Try clearing NuGet cache: `nuget locals all -clear`

2. **Build fails with missing references**
   - Ensure all NuGet packages are restored
   - Check that referenced projects are built successfully

3. **MSBuild not found**
   - Install Visual Studio or Visual Studio Build Tools
   - Ensure MSBuild is in the system PATH

4. **Private package access denied**
   - Verify the Personal Access Token in NuGet.config is valid
   - Check permissions on the Azure DevOps feed

### Manual Build Steps

If the automated scripts fail, you can build manually:

1. **Restore packages**:
   ```cmd
   nuget restore Jitb.Employment.sln
   ```

2. **Build solution**:
   ```cmd
   msbuild Jitb.Employment.sln /p:Configuration=Debug /p:Platform="Any CPU"
   ```

## Environment-Specific Configurations

The solution includes configuration transforms for different environments:

- **Local**: Default development settings
- **QA**: Quality assurance environment
- **Production**: Production environment
- **AWS**: Amazon Web Services environments (qa, prod)

Configuration files are transformed during the packaging process based on the target environment.

## Dependencies

### Key Technologies
- **NServiceBus 7.4.0** - Messaging framework
- **NHibernate 4.0.4** - ORM for database access
- **StructureMap 4.7.1** - Dependency injection
- **AutoMapper 8.1.1** - Object mapping
- **Entity Framework 6.2.0** - Additional ORM support

### External Services
- **Amazon SQS** - Message transport
- **SQL Server** - Primary database
- **Oracle** - Legacy system integration
- **Active Directory** - User authentication and management

## Support

For build issues or questions, please contact the development team or refer to the project documentation.