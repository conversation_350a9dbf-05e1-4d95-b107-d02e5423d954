Write-Host "=== Jitb.Employment Solution Health Check ===" -ForegroundColor Green
Write-Host ""

# Get the script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$SolutionFile = Join-Path $ScriptDir "Jitb.Employment.sln"

# Check if solution file exists
Write-Host "1. Checking solution file..." -ForegroundColor Cyan
if (Test-Path $SolutionFile) {
    Write-Host "   ✓ Solution file found: Jitb.Employment.sln" -ForegroundColor Green
    
    # Parse solution file to get projects
    $solutionContent = Get-Content $SolutionFile
    $projectLines = $solutionContent | Where-Object { $_ -match 'Project\(' }
    Write-Host "   ✓ Found $($projectLines.Count) projects in solution" -ForegroundColor Green
}
else {
    Write-Host "   ✗ Solution file not found!" -ForegroundColor Red
    exit 1
}

# Check source directory
Write-Host ""
Write-Host "2. Checking source directory..." -ForegroundColor Cyan
$srcPath = Join-Path $ScriptDir "src"
if (Test-Path $srcPath) {
    $projects = Get-ChildItem $srcPath -Directory
    Write-Host "   ✓ Source directory found with $($projects.Count) project folders" -ForegroundColor Green
    
    # Check each project for .csproj files
    $csprojCount = 0
    $missingProjects = @()
    
    foreach ($project in $projects) {
        $csprojFiles = Get-ChildItem $project.FullName -Filter "*.csproj"
        if ($csprojFiles.Count -gt 0) {
            $csprojCount++
        }
        else {
            $missingProjects += $project.Name
        }
    }
    
    Write-Host "   ✓ Found $csprojCount project files (.csproj)" -ForegroundColor Green
    
    if ($missingProjects.Count -gt 0) {
        Write-Host "   ⚠ Folders without .csproj files:" -ForegroundColor Yellow
        $missingProjects | ForEach-Object { Write-Host "     - $_" -ForegroundColor Yellow }
    }
}
else {
    Write-Host "   ✗ Source directory not found!" -ForegroundColor Red
}

# Check packages directory
Write-Host ""
Write-Host "3. Checking packages..." -ForegroundColor Cyan
$packagesPath = Join-Path $ScriptDir "packages"
if (Test-Path $packagesPath) {
    $packageDirs = Get-ChildItem $packagesPath -Directory
    Write-Host "   ✓ Packages directory found with $($packageDirs.Count) packages" -ForegroundColor Green
}
else {
    Write-Host "   ⚠ Packages directory not found - packages need to be restored" -ForegroundColor Yellow
}

# Check NuGet.config
Write-Host ""
Write-Host "4. Checking NuGet configuration..." -ForegroundColor Cyan
$nugetConfigPath = Join-Path $ScriptDir "NuGet.config"
if (Test-Path $nugetConfigPath) {
    Write-Host "   ✓ NuGet.config found" -ForegroundColor Green
    
    # Parse NuGet.config to check package sources
    try {
        [xml]$nugetConfig = Get-Content $nugetConfigPath
        $packageSources = $nugetConfig.configuration.packageSources.add
        Write-Host "   ✓ Package sources configured:" -ForegroundColor Green
        foreach ($source in $packageSources) {
            Write-Host "     - $($source.key): $($source.value)" -ForegroundColor White
        }
    }
    catch {
        Write-Host "   ⚠ Could not parse NuGet.config" -ForegroundColor Yellow
    }
}
else {
    Write-Host "   ⚠ NuGet.config not found" -ForegroundColor Yellow
}

# Check build directory
Write-Host ""
Write-Host "5. Checking build scripts..." -ForegroundColor Cyan
$buildPath = Join-Path $ScriptDir "build"
if (Test-Path $buildPath) {
    Write-Host "   ✓ Build directory found" -ForegroundColor Green
    
    $buildScripts = Get-ChildItem $buildPath -Filter "*.ps1"
    if ($buildScripts.Count -gt 0) {
        Write-Host "   ✓ Found $($buildScripts.Count) PowerShell build scripts:" -ForegroundColor Green
        $buildScripts | ForEach-Object { Write-Host "     - $($_.Name)" -ForegroundColor White }
    }
}
else {
    Write-Host "   ⚠ Build directory not found" -ForegroundColor Yellow
}

# Check for common tools
Write-Host ""
Write-Host "6. Checking development tools..." -ForegroundColor Cyan

# Check for .NET CLI
try {
    $dotnetVersion = dotnet --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "   ✓ .NET CLI found (version: $dotnetVersion)" -ForegroundColor Green
    }
}
catch {
    Write-Host "   ⚠ .NET CLI not found" -ForegroundColor Yellow
}

# Check for MSBuild
$MSBuildPaths = @(
    "${env:ProgramFiles}\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles}\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles}\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Enterprise\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
)

$MSBuildFound = $false
foreach ($path in $MSBuildPaths) {
    if (Test-Path $path) {
        Write-Host "   ✓ MSBuild found: $path" -ForegroundColor Green
        $MSBuildFound = $true
        break
    }
}

if (-not $MSBuildFound) {
    Write-Host "   ⚠ MSBuild not found - Visual Studio may not be installed" -ForegroundColor Yellow
}

# Check for NuGet
try {
    $nugetResult = Get-Command "nuget.exe" -ErrorAction SilentlyContinue
    if ($nugetResult) {
        Write-Host "   ✓ NuGet CLI found: $($nugetResult.Source)" -ForegroundColor Green
    }
    else {
        Write-Host "   ⚠ NuGet CLI not found in PATH" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "   ⚠ NuGet CLI not found" -ForegroundColor Yellow
}

# Check .NET Framework
Write-Host ""
Write-Host "7. Checking .NET Framework..." -ForegroundColor Cyan
try {
    $dotNetVersions = Get-ChildItem "HKLM:SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full\" -ErrorAction SilentlyContinue | Get-ItemProperty -Name Release -ErrorAction SilentlyContinue
    $net461Plus = $dotNetVersions | Where-Object { $_.Release -ge 461808 }
    
    if ($net461Plus) {
        Write-Host "   ✓ .NET Framework 4.6.1+ is installed" -ForegroundColor Green
    }
    else {
        Write-Host "   ⚠ .NET Framework 4.6.1+ may not be installed" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "   ⚠ Could not check .NET Framework version" -ForegroundColor Yellow
}

# Summary
Write-Host ""
Write-Host "=== Summary ===" -ForegroundColor Green
Write-Host "The solution appears to be a .NET Framework 4.6.1 application using:" -ForegroundColor White
Write-Host "- NServiceBus for messaging" -ForegroundColor White
Write-Host "- Multiple endpoints and web services" -ForegroundColor White
Write-Host "- Custom build and deployment scripts" -ForegroundColor White
Write-Host "- Private NuGet packages from Azure DevOps" -ForegroundColor White
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Run .\build_comprehensive.ps1 to attempt building" -ForegroundColor White
Write-Host "2. If build fails, check package restoration" -ForegroundColor White
Write-Host "3. Verify access to private NuGet feed" -ForegroundColor White
Write-Host "4. Check BUILD_README.md for detailed instructions" -ForegroundColor White