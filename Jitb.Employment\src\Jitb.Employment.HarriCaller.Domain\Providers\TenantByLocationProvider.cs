﻿using Afterman.nRepo;
using Jitb.Employment.Domain.Concepts.Config;
using Jitb.Employment.Domain.Dictionaries;
using Jitb.Employment.Domain.Repositories.Config;
using Jitb.Employment.HarriCaller.Domain.Concepts;
using Jitb.Employment.HarriCaller.Domain.Enums;
using NLog;
using RestSharp;
using System;
using System.Threading.Tasks;

namespace Jitb.Employment.HarriCaller.Domain.Providers
{
    public interface ITenantByLocationProvider
    {
        Task<Guid?> GetTenantIdByLocation(int location);
        Task UpdateTenantByLocation();
    }

    public class TenantByLocationProvider : ITenantByLocationProvider
    {
        private readonly IHarriTenantTable _harriTenantTable;
        private readonly ICallHarriWebserviceDeserialize<HarriLocations2List> _callHarriWebServiceProvider;  // not currently used
        private readonly IHarriTenantByLocationRepository _harriTenantByLocationRepository;
        //       private readonly IHarriUrlGenerator _urlGenerator;

        private readonly ILogger _log;


        public TenantByLocationProvider(IHarriTenantTable harriTenantTable,
            ICallHarriWebserviceDeserialize<HarriLocations2List> callHarriWebServiceProvider,
            IHarriTenantByLocationRepository harriTenantByLocationRepository,
            // IHarriUrlGenerator urlGenerator,
            ILogger log)
        {
            _harriTenantTable = harriTenantTable;
            _callHarriWebServiceProvider = callHarriWebServiceProvider;
            _harriTenantByLocationRepository = harriTenantByLocationRepository;
            //   _urlGenerator = urlGenerator;
            _log = log;
        }

        public async Task<Guid?> GetTenantIdByLocation(int location)
        {
            var tenant = _harriTenantByLocationRepository.GetTenantByLocation(location);
            if (tenant == null)
            {
                await UpdateTenantByLocation();
                tenant = _harriTenantByLocationRepository.GetTenantByLocation(location);
            }

            return tenant?.TenantId;
        }

        public async Task UpdateTenantByLocation()
        {
            _log.Info($" Updating TenantByLocation table.");

            var urlGenerator = new HarriUrlGeneratorFactory("").CreateGenerator(UrlType.LocationList);
            foreach (var tenant in _harriTenantTable.GetAll())  // GetAll() only returns Active tenants
            {
                var response = await _callHarriWebServiceProvider.Call(tenant, Method.Get, urlGenerator.Generate(null), null, false, "V1");
                var locations = await _callHarriWebServiceProvider.Deserialize<HarriLocations2List>(response);
                foreach (var l in locations)
                {
                    if (l.Type.ToLower() == "child")
                    {
                        var tenantByLocation = _harriTenantByLocationRepository.GetTenantByLocation(l.Id) ??
                                               new HarriTenantByLocation();
                        tenantByLocation.Location = l.Id;
                        tenantByLocation.TenantId = tenant.TenantId;
                        tenantByLocation.UpdateDate = DateTime.Now;

                        _harriTenantByLocationRepository.Add(tenantByLocation);
                    }
                }

            }
        }
    }
}
