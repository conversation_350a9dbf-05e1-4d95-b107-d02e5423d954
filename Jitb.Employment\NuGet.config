﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<packageRestore>
		<add key="enabled" value="True" />
		<add key="automatic" value="False" />
	</packageRestore>
	<activePackageSource>
		<add key="All" value="(Aggregate source)" />
	</activePackageSource>
	<packageSources>
		<!-- Ditch all the Global NuGet package sources we only want a 
         single private NuGet repo for this project -->
		<clear />
		<add key="nuget.org" value="https://api.nuget.org/v3/index.json" />
		<add key="JackInTheBox-NServiceBus" value="https://pkgs.dev.azure.com/jitb/_packaging/JackInTheBox-NServiceBus/nuget/v3/index.json" />
	</packageSources>
	  <packageSourceCredentials>
    <JackInTheBox-NServiceBus>
      <add key="Username" value="PAT" />
      <add key="ClearTextPassword" value="5bmM2lQvNG99oiShZ5TqVqNCqnQKVxUk1QcCmn5RFOJjwoLkh0a2JQQJ99BAACAAAAAAAAAAAAASAZDOiJ5s" />
    </JackInTheBox-NServiceBus>
  </packageSourceCredentials>
</configuration>
