﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Afterman.nRepo" version="2024.9.5.5" targetFramework="net48" />
  <package id="Antlr3.Runtime" version="3.5.1" targetFramework="net48" />
  <package id="AutoFixture" version="4.18.1" targetFramework="net48" />
  <package id="AutoFixture.AutoMoq" version="4.18.1" targetFramework="net48" />
  <package id="AutoFixture.Xunit2" version="4.18.1" targetFramework="net48" />
  <package id="Castle.Core" version="5.2.1" targetFramework="net48" />
  <package id="Fare" version="2.1.1" targetFramework="net48" />
  <package id="FluentAssertions" version="6.12.1" targetFramework="net48" />
  <package id="FluentNHibernate" version="2.0.3.0" targetFramework="net48" />
  <package id="Iesi.Collections" version="4.0.4" targetFramework="net48" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="8.0.0" targetFramework="net48" />
  <package id="Microsoft.CSharp" version="4.7.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="6.0.0" targetFramework="net48" />
  <package id="Microsoft.TestPlatform.ObjectModel" version="17.12.0" targetFramework="net48" />
  <package id="Moq" version="4.20.72" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net48" />
  <package id="NHibernate" version="5.3.3" targetFramework="net48" />
  <package id="NLog" version="4.5.11" targetFramework="net48" />
  <package id="Pipelines.Sockets.Unofficial" version="2.2.8" targetFramework="net48" />
  <package id="Remotion.Linq" version="2.2.0" targetFramework="net48" />
  <package id="Remotion.Linq.EagerFetching" version="2.2.0" targetFramework="net48" />
  <package id="RestSharp" version="112.0.0" targetFramework="net48" />
  <package id="ServiceStack.Common" version="8.8.0" targetFramework="net48" />
  <package id="ServiceStack.Interfaces" version="8.8.0" targetFramework="net48" />
  <package id="ServiceStack.Redis" version="8.8.0" targetFramework="net48" />
  <package id="ServiceStack.Text" version="8.8.0" targetFramework="net48" />
  <package id="StructureMap" version="4.7.1" targetFramework="net48" />
  <package id="System.Buffers" version="4.6.0" targetFramework="net48" />
  <package id="System.Collections.Immutable" version="1.5.0" targetFramework="net48" />
  <package id="System.Diagnostics.DiagnosticSource" version="8.0.0" targetFramework="net48" />
  <package id="System.IO.Compression" version="4.3.0" targetFramework="net48" />
  <package id="System.IO.Pipelines" version="5.0.1" targetFramework="net48" />
  <package id="System.Memory" version="4.6.0" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.6.0" targetFramework="net48" />
  <package id="System.Reflection.Emit.Lightweight" version="4.3.0" targetFramework="net48" />
  <package id="System.Reflection.Metadata" version="1.6.0" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.1.0" targetFramework="net48" />
  <package id="System.Text.Encodings.Web" version="8.0.0" targetFramework="net48" />
  <package id="System.Text.Json" version="8.0.4" targetFramework="net48" />
  <package id="System.Threading.Channels" version="5.0.0" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.6.0" targetFramework="net48" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net48" />
  <package id="xunit" version="2.9.3" targetFramework="net48" />
  <package id="xunit.abstractions" version="2.0.3" targetFramework="net48" />
  <package id="xunit.analyzers" version="1.18.0" targetFramework="net48" developmentDependency="true" />
  <package id="xunit.assert" version="2.9.3" targetFramework="net48" />
  <package id="xunit.core" version="2.9.3" targetFramework="net48" />
  <package id="xunit.extensibility.core" version="2.9.3" targetFramework="net48" />
  <package id="xunit.extensibility.execution" version="2.9.3" targetFramework="net48" />
  <package id="xunit.runner.visualstudio" version="3.0.2" targetFramework="net48" developmentDependency="true" />
</packages>