using System.Threading.Tasks;

using System.Threading.Tasks;

namespace Jitb.Employment.Endpoint.Handlers
{
    using Contracts.Commands.Employment;
    using Contracts.Events.Employment;
    using Facade.Actions.Core;
    using Jitb.Employment.Domain.Exceptions;
    using Jitb.Employment.Domain.Providers;
    using Jitb.Employment.Domain.Repositories.Employment;
    using NServiceBus;
    using NServiceBus.Logging;
    using System;

    public class ChangePayRateHandler :
        IHandleMessages<ChangePayRate>
    {
        private static readonly ILog Log = LogManager.GetLogger<ChangePayRateHandler>();
        private readonly IEmployeeChangePayRateProvider _employeeChangePayRateProvider;
        private readonly IJobCodePayRateProvider _jobCodePayRateProvider;
        private readonly IEmployeeRepository _employeeRepository;

        public ChangePayRateHandler(IEmployeeChangePayRateProvider employeeChangePayRateProvider
            , IJobCodePayRateProvider jobCodePayRateProvider
            , IEmployeeRepository employeeRepository)
        {
            this._employeeChangePayRateProvider = employeeChangePayRateProvider;
            this._jobCodePayRateProvider = jobCodePayRateProvider;
            this._employeeRepository = employeeRepository;
        }

        public async Task Handle(ChangePayRate message, IMessageHandlerContext context)
        {

            //if (message.EffectiveDate.HasValue && message.EffectiveDate.Value.Date > DateTime.Now.Date)
            //{
            //    Log.Info($"Rate Change is for future date.  Delaying message to be replayed at {message.EffectiveDate?.Date}, employee {message.EmployeeId}, future pay rate {message.PayRate}");
            //    var options = new SendOptions();
            //    options.DelayDeliveryWith(message.EffectiveDate.Value.Date.Subtract(DateTime.Now.Date));
            //    await context.Send(message, options);
            //    return;
            //}

            Log.Info($"Handling ChangePayRate for employeeId {message.EmployeeId}");
            var employee = this._employeeRepository.Get(message.EmployeeId);
            if (null == employee)
                throw new EmployeeNotFoundException(message.EmployeeId, message.GetType().Name, message.AdditionalData);

            //            var effectiveDate = _jobCodePayRateProvider.FindUnusedEffectiveDate(message.EffectiveDate, employee.BadgeId);
            var effectiveDate = message.EffectiveDate;

            employee = this._employeeChangePayRateProvider.ChangePayRate(message.EmployeeId
                , message.PayRate
                , effectiveDate
                , message.AuditKey);

            if (employee?.HasPayRateChange ?? false)
            {
                await context.Publish<IChangedAnEmployeePayRate>(x =>
                {
                    x.EmployeeId = message.EmployeeId;
                    x.EffectiveDate = effectiveDate ?? DateTime.Today;
                    x.EntityId = employee.LegalCompanyId();
                    x.Concept = employee.HRCompanyBrand;
                    x.JobCode = (employee.JobCodeByDate(effectiveDate ?? DateTime.Today)?.JobCode?.Code) ?? "";
                    x.PayRate = message.PayRate;
                    x.DateSent = DateTime.Now;
                });
            }
        }
    }
}