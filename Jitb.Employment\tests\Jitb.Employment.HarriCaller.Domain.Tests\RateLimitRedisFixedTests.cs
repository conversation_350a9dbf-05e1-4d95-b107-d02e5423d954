using System;
using System.Configuration;
using System.Threading.Tasks;
using FluentAssertions;
using Jitb.Employment.HarriCaller.Domain.RateLimiting;
using NLog;
using ServiceStack.Redis;
using Xunit;
using Xunit.Abstractions;

namespace Jitb.Employment.HarriCaller.Domain.Tests
{
    public class RateLimitRedisFixedTests : IDisposable
    {
        private readonly ITestOutputHelper _output;
        private RateLimitRedisFixed _rateLimiter;
        private readonly ILogger _logger;
        private readonly string _redisConnectionString;

        public RateLimitRedisFixedTests(ITestOutputHelper output)
        {
            _output = output;
            _logger = LogManager.GetCurrentClassLogger();
            _redisConnectionString = ConfigurationManager.ConnectionStrings["RedisConnection"]?.ConnectionString
                                      ?? ConfigurationManager.ConnectionStrings["Redis"]?.ConnectionString
                                      ?? "localhost:6379";
            _output.WriteLine($"Using Redis connection: {_redisConnectionString}");
        }

        private async Task<bool> IsRedisAvailable()
        {
            try
            {
                using (var redisClient = new RedisClient(_redisConnectionString))
                {
                    redisClient.Ping();
                    return true;
                }
            }
            catch (Exception ex)
            {
                _output.WriteLine($"Redis not available: {ex.Message}");
                return false;
            }
        }

        private async Task<RateLimitRedisFixed> CreateRateLimiterIfAvailable()
        {
            if (!await IsRedisAvailable())
            {
                _output.WriteLine("Redis is not available. Skipping test.");
                return null;
            }

            try
            {
                return new RateLimitRedisFixed(_redisConnectionString, _logger, 3, "test-rate-limit-fixed:");
            }
            catch (Exception ex)
            {
                _output.WriteLine($"Error creating rate limiter: {ex.Message}");
                return null;
            }
        }

        [Fact]
        public async Task TryMakeRequestAsync_WithinLimit_ShouldReturnTrue()
        {
            _rateLimiter = await CreateRateLimiterIfAvailable();
            if (_rateLimiter == null) return; // Skip test if Redis not available

            var key = $"test-{Guid.NewGuid()}";

            // First request should succeed
            var result = await _rateLimiter.TryMakeRequestAsync(key);
            result.Should().BeTrue("first request should be allowed");
        }

        [Fact]
        public async Task TryMakeRequestAsync_ExceedingLimit_ShouldReturnFalse()
        {
            _rateLimiter = await CreateRateLimiterIfAvailable();
            if (_rateLimiter == null) return; // Skip test if Redis not available

            var key = $"test-{Guid.NewGuid()}";

            // Make 3 requests (our limit)
            for (int i = 0; i < 3; i++)
            {
                var result = await _rateLimiter.TryMakeRequestAsync(key);
                result.Should().BeTrue($"request {i + 1} should be allowed");
            }

            // 4th request should be denied
            var deniedResult = await _rateLimiter.TryMakeRequestAsync(key);
            deniedResult.Should().BeFalse("request exceeding limit should be denied");
        }

        [Fact]
        public async Task TryMakeRequestAsync_RapidRequests_ShouldHandleCollisions()
        {
            _rateLimiter = await CreateRateLimiterIfAvailable();
            if (_rateLimiter == null) return; // Skip test if Redis not available

            var key = $"test-rapid-{Guid.NewGuid()}";

            // Make rapid requests to test for timestamp collisions
            var tasks = new Task<bool>[5];
            for (int i = 0; i < 5; i++)
            {
                tasks[i] = _rateLimiter.TryMakeRequestAsync(key);
            }

            var results = await Task.WhenAll(tasks);

            // With limit of 3, we should get 3 true and 2 false
            var allowedCount = 0;
            var deniedCount = 0;

            foreach (var result in results)
            {
                if (result)
                    allowedCount++;
                else
                    deniedCount++;
            }

            allowedCount.Should().Be(3, "exactly 3 requests should be allowed");
            deniedCount.Should().Be(2, "exactly 2 requests should be denied");
        }

        [Fact]
        public async Task CanMakeRequestAsync_WithinLimit_ShouldReturnTrue()
        {
            _rateLimiter = await CreateRateLimiterIfAvailable();
            if (_rateLimiter == null) return; // Skip test if Redis not available

            var key = $"test-can-{Guid.NewGuid()}";

            // Record a couple requests using TryMakeRequestAsync
            var result1 = await _rateLimiter.TryMakeRequestAsync(key);
            var result2 = await _rateLimiter.TryMakeRequestAsync(key);

            result1.Should().BeTrue("First request should be allowed");
            result2.Should().BeTrue("Second request should be allowed");

            // Should still be able to make one more
            var canMake = await _rateLimiter.CanMakeRequestAsync(key);
            canMake.Should().BeTrue("should be able to make request within limit");
        }

        [Fact]
        public async Task CanMakeRequestAsync_AtLimit_ShouldReturnFalse()
        {
            _rateLimiter = await CreateRateLimiterIfAvailable();
            if (_rateLimiter == null) return; // Skip test if Redis not available

            var key = $"test-can-limit-{Guid.NewGuid()}";

            // Record 3 requests (our limit) using TryMakeRequestAsync
            for (int i = 0; i < 3; i++)
            {
                var result = await _rateLimiter.TryMakeRequestAsync(key);
                result.Should().BeTrue($"Request {i + 1} should be allowed");
            }

            // Should not be able to make another
            var canMake = await _rateLimiter.CanMakeRequestAsync(key);
            canMake.Should().BeFalse("should not be able to make request at limit");
        }

        [Fact]
        public async Task TryMakeRequestAsync_ShouldNotThrow()
        {
            _rateLimiter = await CreateRateLimiterIfAvailable();
            if (_rateLimiter == null) return; // Skip test if Redis not available

            var key = $"test-record-{Guid.NewGuid()}";

            // Recording should not throw
            var result1 = await _rateLimiter.TryMakeRequestAsync(key);
            var result2 = await _rateLimiter.TryMakeRequestAsync(key);
            var result3 = await _rateLimiter.TryMakeRequestAsync(key);

            // All should succeed within limit
            result1.Should().BeTrue("First request should be allowed");
            result2.Should().BeTrue("Second request should be allowed");
            result3.Should().BeTrue("Third request should be allowed");
        }

        [Fact]
        public async Task DifferentKeys_ShouldHaveIndependentLimits()
        {
            _rateLimiter = await CreateRateLimiterIfAvailable();
            if (_rateLimiter == null) return; // Skip test if Redis not available

            var key1 = $"test-key1-{Guid.NewGuid()}";
            var key2 = $"test-key2-{Guid.NewGuid()}";

            // Exhaust limit for key1
            for (int i = 0; i < 3; i++)
            {
                await _rateLimiter.TryMakeRequestAsync(key1);
            }

            // key1 should be at limit
            var key1Result = await _rateLimiter.TryMakeRequestAsync(key1);
            key1Result.Should().BeFalse("key1 should be at limit");

            // key2 should still be available
            var key2Result = await _rateLimiter.TryMakeRequestAsync(key2);
            key2Result.Should().BeTrue("key2 should have independent limit");
        }

        [Fact]
        public async Task TryMakeRequestAsync_ShouldWriteToRedis()
        {
            _rateLimiter = await CreateRateLimiterIfAvailable();
            if (_rateLimiter == null) return; // Skip test if Redis not available

            var key = $"test-redis-verify-{Guid.NewGuid()}";
            var fullKey = $"test-rate-limit-fixed:{key}";

            // Record a request using TryMakeRequestAsync
            var result = await _rateLimiter.TryMakeRequestAsync(key);
            result.Should().BeTrue("Request should be allowed");

            // Connect directly to Redis and verify the key exists and has at least one entry
            using (var redisClient = new RedisClient(_redisConnectionString))
            {
                var length = redisClient.GetAllKeys().Count;
                _output.WriteLine($"Redis key count: {length}");
                length.Should().BeGreaterThan(0, "Redis should have at least one key after TryMakeRequestAsync");
            }
        }

        public void Dispose()
        {
            _rateLimiter?.Dispose();
        }
    }
}
