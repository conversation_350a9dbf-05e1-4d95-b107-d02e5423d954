param(
    [string]$Configuration = "Debug"
)

Write-Host "Building Jitb.Employment solution with dotnet build..." -ForegroundColor Green
Write-Host "Configuration: $Configuration" -ForegroundColor Yellow
Write-Host ""

# Restore packages first
Write-Host "Restoring NuGet packages..." -ForegroundColor Cyan
dotnet restore Jitb.Employment.sln --verbosity normal

if ($LASTEXITCODE -ne 0) {
    Write-Host "Package restore failed. Trying with --force..." -ForegroundColor Yellow
    dotnet restore Jitb.Employment.sln --force --verbosity normal
}

if ($LASTEXITCODE -ne 0) {
    Write-Host "Package restore still failing. Continuing with build anyway..." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Building solution..." -ForegroundColor Cyan
dotnet build Jitb.Employment.sln --configuration $Configuration --verbosity normal --no-restore

if ($LASTEXITCODE -eq 0) {
    Write-Host ""
    Write-Host "✓ Build completed successfully!" -ForegroundColor Green
} else {
    Write-Host ""
    Write-Host "✗ Build failed with exit code: $LASTEXITCODE" -ForegroundColor Red
    Write-Host ""
    Write-Host "Common solutions:" -ForegroundColor Yellow
    Write-Host "1. Check if all NuGet packages are accessible" -ForegroundColor White
    Write-Host "2. Verify Azure DevOps credentials in NuGet.config" -ForegroundColor White
    Write-Host "3. Try: dotnet nuget locals all --clear" -ForegroundColor White
    Write-Host "4. Try: dotnet restore --force" -ForegroundColor White
}