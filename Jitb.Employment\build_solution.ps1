param(
    [string]$Configuration = "Debug",
    [string]$Platform = "Any CPU"
)

Write-Host "Starting build process..."
Write-Host "Configuration: $Configuration"
Write-Host "Platform: $Platform"

# Get the script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$SolutionFile = Join-Path $ScriptDir "Jitb.Employment.sln"

# Check if solution file exists
if (-not (Test-Path $SolutionFile)) {
    Write-Error "Solution file not found: $SolutionFile"
    exit 1
}

Write-Host "Solution file: $SolutionFile"

# Try to find MSBuild
$MSBuildPaths = @(
    "${env:ProgramFiles}\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles}\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles}\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Enterprise\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2017\Enterprise\MSBuild\15.0\Bin\MSBuild.exe",
    "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2017\Professional\MSBuild\15.0\Bin\MSBuild.exe",
    "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2017\Community\MSBuild\15.0\Bin\MSBuild.exe",
    "${env:ProgramFiles(x86)}\MSBuild\14.0\Bin\MSBuild.exe"
)

$MSBuildPath = $null
foreach ($path in $MSBuildPaths) {
    if (Test-Path $path) {
        $MSBuildPath = $path
        break
    }
}

if (-not $MSBuildPath) {
    Write-Error "MSBuild not found. Please install Visual Studio or Build Tools."
    exit 1
}

Write-Host "Using MSBuild: $MSBuildPath"

# Try to find NuGet
$NuGetPaths = @(
    "nuget.exe",
    "${env:ProgramFiles}\NuGet\nuget.exe",
    "${env:ProgramFiles(x86)}\NuGet\nuget.exe"
)

$NuGetPath = $null
foreach ($path in $NuGetPaths) {
    try {
        $result = Get-Command $path -ErrorAction SilentlyContinue
        if ($result) {
            $NuGetPath = $result.Source
            break
        }
    }
    catch {
        # Continue to next path
    }
}

if (-not $NuGetPath) {
    Write-Host "NuGet not found in PATH. Trying to download..."
    $NuGetPath = Join-Path $ScriptDir "nuget.exe"
    if (-not (Test-Path $NuGetPath)) {
        try {
            Write-Host "Downloading NuGet..."
            Invoke-WebRequest -Uri "https://dist.nuget.org/win-x86-commandline/latest/nuget.exe" -OutFile $NuGetPath
        }
        catch {
            Write-Error "Failed to download NuGet: $_"
            exit 1
        }
    }
}

Write-Host "Using NuGet: $NuGetPath"

# Restore NuGet packages
Write-Host "Restoring NuGet packages..."
try {
    & $NuGetPath restore $SolutionFile -NonInteractive
    if ($LASTEXITCODE -ne 0) {
        Write-Warning "NuGet restore completed with warnings or errors (Exit code: $LASTEXITCODE)"
    }
}
catch {
    Write-Error "Failed to restore NuGet packages: $_"
    exit 1
}

# Build the solution
Write-Host "Building solution..."
try {
    & $MSBuildPath $SolutionFile /p:Configuration=$Configuration /p:Platform="$Platform" /m /v:m
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Build failed with exit code: $LASTEXITCODE"
        exit 1
    }
}
catch {
    Write-Error "Failed to build solution: $_"
    exit 1
}

Write-Host "Build completed successfully!"