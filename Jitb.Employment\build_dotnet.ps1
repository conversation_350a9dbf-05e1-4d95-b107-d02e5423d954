param(
    [string]$Configuration = "Debug"
)

Write-Host "Starting build process using dotnet CLI..."
Write-Host "Configuration: $Configuration"

# Get the script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$SolutionFile = Join-Path $ScriptDir "Jitb.Employment.sln"

# Check if solution file exists
if (-not (Test-Path $SolutionFile)) {
    Write-Error "Solution file not found: $SolutionFile"
    exit 1
}

Write-Host "Solution file: $SolutionFile"

# Check if dotnet CLI is available
try {
    $dotnetVersion = dotnet --version
    Write-Host "Using .NET CLI version: $dotnetVersion"
}
catch {
    Write-Error ".NET CLI not found. Please install .NET SDK."
    exit 1
}

# Restore NuGet packages
Write-Host "Restoring NuGet packages..."
try {
    dotnet restore $SolutionFile --verbosity minimal
    if ($LASTEXITCODE -ne 0) {
        Write-Warning "Package restore completed with warnings (Exit code: $LASTEXITCODE)"
    }
}
catch {
    Write-Error "Failed to restore packages: $_"
    exit 1
}

# Build the solution
Write-Host "Building solution..."
try {
    dotnet build $SolutionFile --configuration $Configuration --verbosity minimal --no-restore
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Build failed with exit code: $LASTEXITCODE"
        exit 1
    }
}
catch {
    Write-Error "Failed to build solution: $_"
    exit 1
}

Write-Host "Build completed successfully!"